import styled from 'styled-components/native';
import { Dimensions, Platform } from 'react-native';
import { PADDING, HEIGHTS, FONT_SIZES, BORDER_RADIUS, WIDTHS, isTablet } from '@/constants/responsive';

// Get screen dimensions for responsive calculations
const screenWidth = Dimensions.get('window').width;
const isTabletDevice = isTablet();

// Calculate item width based on screen width
const contentPadding = PADDING.MODAL_CONTENT_HORIZONTAL * 2;
const itemGap = 15;
const itemWidth = (screenWidth - contentPadding - itemGap) / 2;

// Modal container
export const ModalContainer = styled.View`
  width: 100%;
  height: 80%;
  max-height: ${isTabletDevice ? 750 : 650}px;
  background-color: #F0F0F0;
  border-top-left-radius: ${BORDER_RADIUS.MODAL}px;
  border-top-right-radius: ${BORDER_RADIUS.MODAL}px;
  overflow: hidden;
  flex-direction: column;
`;

// Content container
export const ContentContainer = styled.ScrollView`
  width: 100%;
  padding: 0 ${PADDING.MODAL_CONTENT_HORIZONTAL}px;
  flex: 1;
`;

// Frame container for form fields
export const FrameContainer = styled.View`
  width: 100%;
  margin-top: ${isTabletDevice ? 16 : 12}px;
  gap: ${isTabletDevice ? 24 : 16}px;
`;

// Form group
export const FormGroup = styled.View`
  width: 100%;
  gap: ${isTabletDevice ? 24 : 16}px;
`;

// Input field container
export const InputFieldContainer = styled.View`
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  padding: ${PADDING.FIELD_VERTICAL}px ${PADDING.FIELD_HORIZONTAL}px;
  width: 100%;
  height: ${HEIGHTS.FIELD}px;
  background-color: #F0F0F0;
  border: 1px solid #A1A1A1;
  border-radius: ${BORDER_RADIUS.FIELD_GROUP}px;
`;

// Input field label
export const InputFieldLabel = styled.Text`
  font-family: 'MuktaVaani';
  font-weight: 400;
  font-size: ${FONT_SIZES.LABEL}px;
  line-height: 24px;
  color: #333333;
`;

// Input field value
export const InputFieldValue = styled.Text`
  font-family: 'MuktaVaani';
  font-weight: 400;
  font-size: ${FONT_SIZES.INPUT}px;
  line-height: 24px;
  color: #333333;
`;

// Styled TextInput component
export const StyledTextInput = styled.TextInput.attrs({
  textAlignVertical: 'center',
  includeFontPadding: false,
})`
  font-family: 'MuktaVaani';
  font-weight: 400;
  font-size: ${FONT_SIZES.INPUT}px;
  line-height: 24px;
  color: #333333;
  text-align: right;
  width: ${WIDTHS.FIELD_INPUT};
  padding: 0;
  margin: 0;
  border-width: 0;
  background-color: transparent;
`;

// Android-specific TextInput component for location field
export const AndroidLocationTextInput = styled.TextInput.attrs({
  textAlignVertical: 'center',
  includeFontPadding: false,
})`
  font-family: 'MuktaVaani';
  font-weight: 400;
  font-size: ${FONT_SIZES.INPUT}px;
  line-height: 24px;
  color: #333333;
  text-align: ${Platform.OS === 'android' ? 'left' : 'right'};
  width: ${WIDTHS.FIELD_INPUT};
  padding: 0;
  margin: 0;
  border-width: 0;
  background-color: transparent;
`;

// Date button
export const DateButton = styled.TouchableOpacity`
  flex-direction: row;
  justify-content: center;
  align-items: center;
  padding: 4px 8px;
  width: ${Platform.OS === 'android'
    ? (isTabletDevice ? 140 : 120)
    : (isTabletDevice ? 120 : 101)}px;
  height: ${isTabletDevice ? 36 : 32}px;
  background-color: #0E7E61;
  border-radius: 4px;
`;

// Date button text
export const DateButtonText = styled.Text`
  font-family: 'MuktaVaani';
  font-weight: 400;
  font-size: ${FONT_SIZES.INPUT}px;
  line-height: 24px;
  color: #FFFFFF;
  padding-top: 0;
  margin-top: -2px;
  height: 24px;
`;

// Section title
export const SectionTitle = styled.Text`
  font-family: 'MuktaVaani';
  font-weight: 600;
  font-size: ${isTabletDevice ? 22 : 20}px;
  line-height: ${isTabletDevice ? 28 : 24}px;
  color: #333333;
  margin-bottom: 0px;
`;

// Dropdown container
export const DropdownContainer = styled.TouchableOpacity`
  width: 100%;
  height: ${HEIGHTS.FIELD}px;
  border: 1px solid #A1A1A1;
  border-radius: ${BORDER_RADIUS.FIELD_GROUP}px;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
  padding: 0 ${PADDING.FIELD_HORIZONTAL}px;
  background-color: #F0F0F0;
`;

// Dropdown text
export const DropdownText = styled.Text`
  font-family: 'MuktaVaani';
  font-weight: 400;
  font-size: ${FONT_SIZES.INPUT}px;
  line-height: 24px;
  color: #333333;
`;

// Search container
export const SearchContainer = styled.View`
  width: 100%;
  height: 48px;
  border: 1px solid #C0C0C0;
  border-radius: 99px;
  flex-direction: row;
  align-items: center;
  padding: 0 ${PADDING.FIELD_HORIZONTAL}px;
  margin-top: 16px;
`;

// Search input
export const SearchInput = styled.TextInput`
  flex: 1;
  height: 100%;
  font-family: 'MuktaVaani';
  font-size: ${isTabletDevice ? 16 : 14}px;
  color: #333333;
  padding: 0;
  margin-left: 8px;
`;

// Filter container
export const FilterContainer = styled.ScrollView`
  width: 100%;
  margin-top: 16px;
  margin-bottom: 16px;
`;

// Filter button container
export const FilterButtonsContainer = styled.View`
  flex-direction: row;
  gap: 4px;
`;

// Filter button props interface
interface FilterButtonProps {
  active: boolean;
}

// Filter button
export const FilterButton = styled.TouchableOpacity<FilterButtonProps>`
  height: ${isTabletDevice ? 36 : 32}px;
  padding: 8px ${isTabletDevice ? 20 : 16}px;
  border-radius: 8px;
  background-color: ${(props: FilterButtonProps) => props.active ? '#0E7E61' : 'transparent'};
  border: ${(props: FilterButtonProps) => props.active ? 'none' : '1px solid #0E7E61'};
  justify-content: center;
  align-items: center;
  overflow: visible;
  margin-right: ${isTabletDevice ? 8 : 4}px;
`;

// Filter button text
export const FilterButtonText = styled.Text<FilterButtonProps>`
  font-family: 'MuktaVaani';
  font-weight: 500;
  font-size: ${isTabletDevice ? 14 : 12}px;
  line-height: ${isTabletDevice ? 18 : 16}px;
  color: ${(props: FilterButtonProps) => props.active ? '#FFFFFF' : '#0E7E61'};
  text-align: center;
  padding-top: 1px;
  height: ${isTabletDevice ? 18 : 16}px;
`;

// Clothes grid container
export const ClothesGridContainer = styled.View`
  width: 100%;
  flex-direction: row;
  flex-wrap: wrap;
  justify-content: space-between;
  margin-bottom: 24px;
  padding-bottom: 80px;
  min-height: 300px;
`;

// Clothes item container
export const ClothesItemContainer = styled.TouchableOpacity`
  width: ${itemWidth}px;
  height: ${itemWidth + 4}px;
  background-color: #EBEBEB;
  border-radius: 10px;
  margin-bottom: 36px; /* Increased to make room for the label */
  position: relative;
  align-items: center;
  justify-content: center;
  padding: 0;
  overflow: visible; /* Changed to visible to show the label */
`;

// Clothes item image
export const ClothesItemImage = styled.Image`
  width: 100%;
  height: 100%;
  background-color: #F0F0F0;
  border-radius: 8px;
  object-fit: cover;
`;

// Item name text
export const ClothItemName = styled.Text`
  font-size: 16px;
  font-family: MuktaVaani;
  text-align: left;
  width: 100%;
  margin-top: 8px;
  padding-left: 0;
  padding-right: 0;
  color: #333333;
  height: 24px;
  overflow: visible;
  position: absolute;
  bottom: -28px;
  left: 0;
`;

// Checkbox container
export const CheckboxContainer = styled.TouchableOpacity`
  position: absolute;
  bottom: 8px;
  right: 8px;
  width: 24px;
  height: 24px;
  background-color: #FFFFFF;
  border-radius: 4px;
  border: 1px solid #067A46;
  align-items: center;
  justify-content: center;
`;

// Modal header
export const ModalHeader = styled.View`
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  padding: ${isTabletDevice ? 24 : 20}px ${PADDING.MODAL_CONTENT_HORIZONTAL}px ${isTabletDevice ? 16 : 12}px;
  width: 100%;
  height: ${isTabletDevice ? 80 : 72}px;
`;

// Header button
export const HeaderButton = styled.TouchableOpacity`
  height: ${isTabletDevice ? 44 : 40}px;
  padding: ${isTabletDevice ? 12 : 10}px 0;
  justify-content: center;
`;

// Header button text
export const HeaderButtonText = styled.Text`
  font-family: 'MuktaVaani';
  font-weight: 500;
  font-size: ${FONT_SIZES.LABEL}px;
  line-height: 20px;
  color: #0E7E61;
  text-align: center;
`;

// Header title
export const HeaderTitle = styled.Text`
  font-family: 'MuktaVaani';
  font-weight: bold;
  font-size: ${FONT_SIZES.HEADER}px;
  line-height: 32px;
  color: #1C1C1C;
  text-align: center;
  padding-top: 4px;
  height: 36px;
`;

// Bar indicator at the top of the modal
export const BarIndicatorContainer = styled.View`
  width: 100%;
  height: ${isTabletDevice ? 40 : 34}px;
  align-items: center;
  justify-content: center;
`;

export const BarIndicator = styled.View`
  width: ${isTabletDevice ? 64 : 56}px;
  height: ${isTabletDevice ? 6 : 5}px;
  border-radius: ${isTabletDevice ? 6 : 5}px;
  background-color: rgba(51, 51, 51, 0.2);
`;

// Checkbox checked icon
export const CheckboxChecked = styled.View`
  width: ${isTabletDevice ? 16 : 14}px;
  height: ${isTabletDevice ? 16 : 14}px;
  background-color: #067A46;
  border-radius: ${isTabletDevice ? 3 : 2}px;
`;
