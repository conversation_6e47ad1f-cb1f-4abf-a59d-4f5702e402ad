import EditBlackSvg from '@/assets/svg/edit-black.svg';
import EditGraySvg from '@/assets/svg/edit-gray.svg';
import Settings from '@/assets/svg/settings.svg';
import Container from '@/components/common/Container';
import { DefaultAvatar, ProfileBg } from '@/constants/images';
import { UploadImage, uploadImageToS3 } from '@/methods/cloths';
import { getProfile, updateUserProfile } from '@/methods/users';
import * as ImagePicker from 'expo-image-picker';
import { router } from 'expo-router';
import { useCallback, useEffect, useState } from 'react';
import { ActivityIndicator, ImageBackground, View } from 'react-native';
import {
  EditBtn,
  EditBtnContainer,
  EditText,
  NameText,
  ProfileHeaderContainer,
  ProfilePic,
  ProfilePicContainer,
  SettingsIcon,
  UserNameText,
  UserNameTouchable,
} from './styles';

export const ProfileHeader = () => {
  const [avatar, setAvatar] = useState<string | null>(null);
  const [avatarBase64, setAvatarBase64] = useState<string | null>(null);
  const {
    mutate: uploadAvatarMutation,
    isSuccess: isAvatarUploaded,
    data: avatarData,
  } = UploadImage();
  const { mutate: uploadAvatarToS3Mutation, isSuccess: isAvatarUploadedToS3 } =
    uploadImageToS3();
  const { mutate: updateProfile } = updateUserProfile();

  const { data } = getProfile();
  const profileData = data?.data.profile || {};
  const name = profileData.name || '';
  const email = profileData.email || '';
  const userName = profileData.userName || '';
  const profileAvatar = profileData.avatarURL;

  const [displayAvatar, setDisplayAvatar] = useState<string | null>(
    profileAvatar,
  );
  const [isAvatarLoading, setIsAvatarLoading] = useState(false);

  useEffect(() => {
    if (avatar) {
      setDisplayAvatar(avatar);
    } else if (profileAvatar) {
      setDisplayAvatar(profileAvatar);
    }
    setIsAvatarLoading(false);
  }, [avatar, profileAvatar]);

  const handleChangeProfilePicture = useCallback(async () => {
    try {
      const { status } =
        await ImagePicker.requestMediaLibraryPermissionsAsync();

      if (status !== 'granted') {
        alert('Permission to access media library is required!');
        return;
      }

      const result = await ImagePicker.launchImageLibraryAsync({
        mediaTypes: ImagePicker.MediaType.Images,
        allowsEditing: true,
        aspect: [1, 1],
        base64: true,
        quality: 1,
        selectionLimit: 1,
        exif: false,
      });

      if (!result.canceled && result.assets?.[0]) {
        const selectedImage = result.assets[0];
        selectedImage.base64 && setAvatarBase64(selectedImage.base64);
        setDisplayAvatar(selectedImage.uri);
        uploadAvatarMutation({
          fileName: selectedImage.uri,
          fileType: 'image/jpeg',
          folderPath: 'avatars',
          imageUrl: selectedImage.uri,
        });
      }
    } catch (error) {
      console.error('Error picking image:', error);
      alert('Failed to pick image');
    }
  }, []);

  useEffect(() => {
    if (avatarData && isAvatarUploaded && avatarBase64) {
      uploadAvatarToS3Mutation({
        imageUrl: avatarBase64,
        preSignedUrl: avatarData?.preSignedURL,
      });
    }
  }, [isAvatarUploaded, avatarBase64]);

  useEffect(() => {
    if (isAvatarUploadedToS3 && avatarData) {
      setAvatar(avatarData.fileURL);
      updateProfile({
        avatarURL: avatarData.fileURL,
      });
    }
  }, [isAvatarUploadedToS3, avatarData]);

  const handleChangeUsername = useCallback(() => {
    router.push('/change-username');
  }, []);

  return (
    <ImageBackground source={ProfileBg} resizeMode="cover">
      <Container edges={['top']}>
        <ProfileHeaderContainer>
          <SettingsIcon onPress={() => router.push('/(tabs)/settings')}>
            <Settings />
          </SettingsIcon>
          <ProfilePicContainer>
            <ProfilePic
              source={displayAvatar ? { uri: displayAvatar } : DefaultAvatar}
              onLoadEnd={() => setIsAvatarLoading(false)}
            />
            {isAvatarLoading && (
              <View
                style={{
                  position: 'absolute',
                  top: 0,
                  left: 0,
                  right: 0,
                  bottom: 0,
                  justifyContent: 'center',
                  alignItems: 'center',
                }}
              >
                <ActivityIndicator size="large" color="#0E7E61" />
              </View>
            )}
            <EditBtnContainer>
              <EditBtn activeOpacity={0.8} onPress={handleChangeProfilePicture}>
                <EditText>Edit</EditText>
                <EditBlackSvg />
              </EditBtn>
            </EditBtnContainer>
          </ProfilePicContainer>
          <NameText numberOfLines={1}>{name || email}</NameText>
          <UserNameTouchable activeOpacity={0.8} onPress={handleChangeUsername}>
            <UserNameText numberOfLines={1}>@{userName || email}</UserNameText>
            <EditGraySvg />
          </UserNameTouchable>
        </ProfileHeaderContainer>
      </Container>
    </ImageBackground>
  );
};
